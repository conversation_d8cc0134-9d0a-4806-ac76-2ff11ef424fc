'use client';

import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import ThemeToggle from "@/components/shared/theme/ThemeToggle";
import LanguageToggle from "@/components/shared/language/LanguageToggle";
import { LogIn, LogOut, User } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";

export default function Header() {
  const t = useTranslations('HomePage');
  const { isAuthenticated, user, signOut, goToLogin, goToSignup } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className="flex justify-between items-center p-6 bg-background border-b">
      <div className="flex items-center gap-4">
        <h2 className="text-2xl font-bold">Smash Music</h2>
      </div>
      <div className="flex items-center gap-3">
        {isAuthenticated ? (
          <div className="flex items-center gap-3">
            <span className="text-sm text-muted-foreground">
              Welcome, {user?.email || user?.username}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSignOut}
              className="gap-2"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </Button>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToLogin}
              className="gap-2"
            >
              <LogIn className="w-4 h-4" />
              Sign In
            </Button>
            <Button
              size="sm"
              onClick={goToSignup}
              className="gap-2"
            >
              <User className="w-4 h-4" />
              Sign Up
            </Button>
          </div>
        )}
        <LanguageToggle />
        <ThemeToggle />
      </div>
    </header>
  );
}
